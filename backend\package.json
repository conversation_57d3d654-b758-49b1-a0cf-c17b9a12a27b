{"name": "backend", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "drizzle-kit": "^0.31.4", "prisma": "^6.13.0", "typescript": "^5.5.2", "vitest": "~3.2.0", "wrangler": "^4.27.0"}, "dependencies": {"drizzle-orm": "^0.44.4", "hono": "^4.8.10"}}